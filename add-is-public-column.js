#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Read the JSON file to get is_public data
const jsonPath = path.join(__dirname, 'data', 'solnic-sponsorships-all-time.json');
const csvPath = path.join(__dirname, 'data', 'github-sponsors.csv');

console.log('📄 Reading JSON data...');
const jsonData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));

// Create a map of sponsor_handle -> is_public
const publicMap = {};
jsonData.forEach(sponsor => {
  publicMap[sponsor.sponsor_handle] = sponsor.is_public || false;
});

console.log('📄 Reading CSV data...');
const csvContent = fs.readFileSync(csvPath, 'utf8');
const lines = csvContent.trim().split('\n');
const headers = lines[0].split(',');

// Check if is_public column already exists
if (headers.includes('is_public')) {
  console.log('✅ is_public column already exists in CSV');
  process.exit(0);
}

console.log('➕ Adding is_public column...');

// Add is_public to headers
const newHeaders = [...headers];
const isActiveIndex = newHeaders.indexOf('is_active');
newHeaders.splice(isActiveIndex + 1, 0, 'is_public');

// Process data rows
const newLines = [newHeaders.join(',')];

for (let i = 1; i < lines.length; i++) {
  const values = lines[i].split(',');
  const sponsorHandle = values[0]; // sponsor_handle is first column
  
  // Insert is_public value after is_active
  const newValues = [...values];
  const isPublic = publicMap[sponsorHandle] ? 'true' : 'false';
  newValues.splice(isActiveIndex + 1, 0, isPublic);
  
  newLines.push(newValues.join(','));
}

// Write the updated CSV
const newCsvContent = newLines.join('\n');
fs.writeFileSync(csvPath, newCsvContent);

console.log('✅ Updated CSV with is_public column');
console.log(`📊 Total sponsors: ${lines.length - 1}`);

// Show a few examples
console.log('\n📋 Sample data:');
console.log(newLines[0]); // headers
for (let i = 1; i <= Math.min(3, newLines.length - 1); i++) {
  console.log(newLines[i]);
}
