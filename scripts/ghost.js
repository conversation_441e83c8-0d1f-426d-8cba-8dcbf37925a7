#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const { program } = require('commander');
const GhostAdminAPI = require('@tryghost/admin-api');

// Command line argument parsing
program
  .name('ghost')
  .description('Sync content with Ghost.io using Admin API')
  .option('--config <file>', 'Ghost config file path', 'ghost.yml')
  .option('--page <name>', 'Update specific page (github-sponsors)')
  .option('--dry-run', 'Show what would be updated without making changes')
  .option('--verbose', 'Show detailed output')
  .addHelpText('after', `
Examples:
  Update GitHub sponsors page:
    $ node scripts/ghost.js --page github-sponsors

  Dry run to see what would be updated:
    $ node scripts/ghost.js --page github-sponsors --dry-run

  Generate HTML fallback if API fails:
    $ node scripts/ghost.js --page github-sponsors --fallback
`)
  .parse();

const options = program.opts();

// Load Ghost configuration
const loadGhostConfig = () => {
  const configPath = path.resolve(options.config);

  if (!fs.existsSync(configPath)) {
    console.error(`❌ Ghost config file not found: ${configPath}`);
    process.exit(1);
  }

  try {
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = yaml.load(configContent);

    if (!config.integrations || !config.integrations.page_sync) {
      console.error('❌ Missing page_sync integration in Ghost config');
      console.error('💡 Add integration config to ghost.yml:');
      console.error('integrations:');
      console.error('  page_sync:');
      console.error('    admin_api_key: "your_admin_api_key"');
      process.exit(1);
    }

    const integration = config.integrations.page_sync;

    if (!integration.admin_api_key) {
      console.error('❌ Missing admin_api_key in Ghost config');
      console.error('💡 Get your Admin API key from Ghost Admin → Settings → Integrations');
      process.exit(1);
    }

    return integration;
  } catch (error) {
    console.error(`❌ Error loading Ghost config: ${error.message}`);
    process.exit(1);
  }
};

// Parse CSV data
const parseCSV = (csvPath) => {
  const content = fs.readFileSync(csvPath, 'utf8');
  const lines = content.trim().split('\n');
  const headers = lines[0].split(',');

  return lines.slice(1).map(line => {
    const values = line.split(',');
    const row = {};
    headers.forEach((header, index) => {
      row[header] = values[index] || '';
    });
    return row;
  });
};

// Generate GitHub profile URL
const githubProfileUrl = (handle) => {
  return `https://github.com/${handle}`;
};

// Generate GitHub Sponsors page HTML with embedded CSS
const generateGitHubSponsorsHTML = () => {
  const csvPath = path.join(__dirname, '..', 'data', 'github-sponsors.csv');

  if (!fs.existsSync(csvPath)) {
    console.error(`❌ GitHub sponsors CSV file not found: ${csvPath}`);
    process.exit(1);
  }

  console.log('📊 Reading GitHub sponsors data...');
  const sponsorsData = parseCSV(csvPath);

  // Process sponsors data - only include public sponsors
  const currentSponsors = sponsorsData
    .filter(sponsor => sponsor.is_active === 'true' && sponsor.is_public === 'true')
    .sort((a, b) => new Date(a.sponsorship_started_on) - new Date(b.sponsorship_started_on))
    .reverse();

  const pastSponsors = sponsorsData
    .filter(sponsor => sponsor.is_active === 'false' && sponsor.is_public === 'true')
    .sort((a, b) => new Date(a.sponsorship_started_on) - new Date(b.sponsorship_started_on))
    .reverse();

  // Generate HTML that matches the existing CSS classes in site header
  let html = '';

  // Current Sponsors Section - simplified HTML structure
  html += `<!-- Last updated: ${new Date().toISOString()} | Current: ${currentSponsors.length} | Past: ${pastSponsors.length} -->\n`;
  html += '<div class="sponsors-section kg-card">\n';
  html += '<h2>Current Sponsors</h2>\n';
  html += `<p>I'm incredibly grateful for the support from my current GitHub Sponsors (${currentSponsors.length} amazing supporters). Your contributions help me dedicate more time to open source work and make it sustainable. Thank you! 🙏</p>\n`;

  if (currentSponsors.length > 0) {
    html += '<div class="sponsor-grid current-sponsors-grid">\n';
    currentSponsors.forEach(sponsor => {
      const profileUrl = githubProfileUrl(sponsor.sponsor_handle);
      const name = sponsor.sponsor_name || sponsor.sponsor_handle;
      html += '<div class="sponsor-card">\n';
      html += `<a href="${profileUrl}" target="_blank" rel="noopener" class="sponsor-link">\n`;
      if (sponsor.avatar_url) {
        html += `<img src="${sponsor.avatar_url}" alt="${name}" class="sponsor-avatar">\n`;
      }
      html += `<h3 class="sponsor-name">${name}</h3>\n`;
      html += '</a>\n';
      html += '</div>\n';
    });
    html += '</div>\n';
  } else {
    html += '<p><em>No current sponsors at the moment.</em></p>\n';
  }
  html += '</div>\n\n';

  // Past Sponsors Section - matches existing CSS classes
  html += '<div class="sponsors-section kg-card">\n';
  html += '  <h2>Past Sponsors</h2>\n';
  html += '  <p>Thank you to all my past sponsors for their support! 🙏</p>\n';

  if (pastSponsors.length > 0) {
    html += '  <ul class="sponsor-list past-sponsors-list">\n';
    pastSponsors.forEach(sponsor => {
      const profileUrl = githubProfileUrl(sponsor.sponsor_handle);
      const name = sponsor.sponsor_name || sponsor.sponsor_handle;
      const handle = sponsor.sponsor_handle;
      html += '    <li class="sponsor-list-item">\n';
      if (sponsor.avatar_url) {
        html += `      <img src="${sponsor.avatar_url}" alt="${name}" class="sponsor-avatar">\n`;
      }
      html += `      <a href="${profileUrl}" target="_blank" rel="noopener" class="sponsor-handle">@${handle}</a>\n`;
      html += '    </li>\n';
    });
    html += '  </ul>\n';
  } else {
    html += '  <p><em>No past sponsors.</em></p>\n';
  }
  html += '</div>\n';

  return {
    html,
    currentSponsors: currentSponsors.length,
    pastSponsors: pastSponsors.length
  };
};

// Initialize Ghost Admin API
const initGhostAPI = (config) => {
  if (options.verbose) {
    console.log('🔧 API Configuration:');
    console.log(`   URL: https://solnic.ghost.io`);
    console.log(`   Key: ${config.admin_api_key.substring(0, 20)}...`);
    console.log(`   Version: v5.0`);
  }

  // Validate API key format (should be id:secret)
  if (!config.admin_api_key.includes(':')) {
    throw new Error('Invalid Admin API key format. Expected format: id:secret');
  }

  return new GhostAdminAPI({
    url: 'https://solnic.ghost.io',
    key: config.admin_api_key,
    version: 'v5.0'
  });
};

// Find page by slug
const findPageBySlug = async (api, slug) => {
  try {
    const pages = await api.pages.browse({
      filter: `slug:${slug}`,
      limit: 1
    });

    return pages.length > 0 ? pages[0] : null;
  } catch (error) {
    console.error(`❌ Error finding page with slug "${slug}":`, error.message);
    throw error;
  }
};

// Update GitHub sponsors page
const updateGitHubSponsorsPage = async (api) => {
  console.log('🔍 Looking for GitHub sponsors page...');

  // Find the existing page
  const existingPage = await findPageBySlug(api, 'github-sponsors');

  if (!existingPage) {
    console.error('❌ GitHub sponsors page not found. Please create it first in Ghost admin.');
    console.error('💡 Create a new page with slug "github-sponsors" in Ghost Admin');
    process.exit(1);
  }

  console.log(`✅ Found page: "${existingPage.title}" (ID: ${existingPage.id})`);

  // Generate new content
  const { html, currentSponsors, pastSponsors } = generateGitHubSponsorsHTML();

  if (options.dryRun) {
    console.log('\n🔍 Dry run - would update page with:');
    console.log(`📊 Current sponsors: ${currentSponsors}`);
    console.log(`📊 Past sponsors: ${pastSponsors}`);
    console.log(`📄 HTML length: ${html.length} characters`);
    console.log('\n🔍 HTML preview (first 300 characters):');
    console.log(html.substring(0, 300) + '...');
    console.log('\n🔍 Dry run completed - no changes were made');
    return;
  }

  try {
    // Fetch the page again to get the latest updated_at timestamp
    console.log('🔄 Fetching latest page data for update...');
    const latestPage = await findPageBySlug(api, 'github-sponsors');

    if (!latestPage) {
      throw new Error('Page not found when trying to update');
    }

    if (options.verbose) {
      console.log(`📄 Current page updated_at: ${latestPage.updated_at}`);
      console.log(`📄 HTML content length: ${html.length} characters`);
      console.log(`📄 Page status: ${latestPage.status}`);
      console.log(`📄 Page visibility: ${latestPage.visibility}`);
    }

    // Update the page using the correct format for HTML content
    // According to Ghost docs, we need to use source=html parameter and wrap data correctly
    const updateData = {
      id: latestPage.id,
      title: latestPage.title, // Keep the same title
      html: html,
      updated_at: latestPage.updated_at // Required for conflict detection
    };

    if (options.verbose) {
      console.log(`📄 Update data keys: ${Object.keys(updateData).join(', ')}`);
    }

    // Use the raw API with source=html parameter to force HTML conversion
    const updatedPage = await api.pages.edit(updateData, {source: 'html'});

    console.log('✅ GitHub sponsors page updated successfully!');
    console.log(`📊 Current sponsors: ${currentSponsors}`);
    console.log(`📊 Past sponsors: ${pastSponsors}`);
    console.log(`🔗 Page URL: https://solnic.dev/${updatedPage.slug}/`);

    if (options.verbose) {
      console.log(`📄 Updated page ID: ${updatedPage.id}`);
      console.log(`📄 New updated_at: ${updatedPage.updated_at}`);
    }

  } catch (error) {
    console.error('❌ Error updating page:', error.message);

    if (error.message.includes('UpdateCollisionError')) {
      console.error('💡 The page was modified by someone else. Please try again.');
    } else if (error.message.includes('Authorization')) {
      console.error('💡 API authorization failed. Check your Admin API key permissions.');
      console.error('💡 Make sure the integration has "Pages" permissions in Ghost Admin.');
    }

    throw error;
  }
};

// Main function
const main = async () => {
  try {
    console.log('🚀 Starting Ghost.io sync...');

    // Load configuration
    const config = loadGhostConfig();
    console.log('✅ Ghost configuration loaded');

    // Initialize Ghost API
    const api = initGhostAPI(config);
    console.log('✅ Ghost Admin API initialized');

    // Handle specific page updates
    if (options.page) {
      if (options.page === 'github-sponsors') {
        try {
          await updateGitHubSponsorsPage(api);
        } catch (error) {
          if (error.message.includes('Authorization') || error.message.includes('401')) {
            console.error('❌ Admin API authorization failed');
            console.error('💡 Falling back to HTML generation mode...');

            // Generate HTML as fallback
            const { html, currentSponsors, pastSponsors } = generateGitHubSponsorsHTML();
            const outputFile = 'github-sponsors-manual-update.html';

            try {
              fs.writeFileSync(outputFile, html);
              console.log('✅ HTML with embedded CSS generated successfully!');
              console.log(`💾 Output saved to: ${outputFile}`);
              console.log(`📊 Current sponsors: ${currentSponsors}`);
              console.log(`📊 Past sponsors: ${pastSponsors}`);
              console.log('\n📋 Manual Update Instructions:');
              console.log('1. Copy the entire content from the generated HTML file');
              console.log('2. Go to Ghost.io Admin → Pages → GitHub Sponsors');
              console.log('3. Delete current content and add an HTML card');
              console.log('4. Paste the HTML content into the HTML card');
              console.log('5. Publish the page');
              console.log('\n💡 To fix API access:');
              console.log('💡 1. Go to https://solnic.ghost.io/ghost/#/settings/integrations');
              console.log('💡 2. Create a new Custom Integration');
              console.log('💡 3. Copy the Admin API Key');
              console.log('💡 4. Update ghost.yml with the new key');
            } catch (writeError) {
              console.error('❌ Error writing HTML file:', writeError.message);
              process.exit(1);
            }
          } else {
            throw error;
          }
        }
      } else {
        console.error(`❌ Unknown page: ${options.page}`);
        console.error('💡 Supported pages: github-sponsors');
        process.exit(1);
      }
      return;
    }

    // If no specific page, show help
    console.log('💡 Please specify a page to update with --page option');
    console.log('💡 Run with --help for more information');

  } catch (error) {
    console.error('❌ Error:', error.message);

    if (options.verbose) {
      console.error('Stack trace:', error.stack);
    }

    process.exit(1);
  }
};

// Run the script
if (require.main === module) {
  main();
}
