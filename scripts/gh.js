#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { program } = require('commander');
const GitHub = require('github-api');

// Command line argument parsing
program
  .name('gh')
  .description('Process manually exported GitHub sponsors data and update CSV/JSON files')
  .option('--input <file>', 'Input JSON file path', 'data/solnic-sponsorships-all-time.json')
  .option('--output <file>', 'Output CSV file path', 'data/github-sponsors.csv')
  .option('--json', 'Also generate JSON output file')
  .option('--dry-run', 'Show what would be written without updating files')
  .option('--verbose', 'Show detailed output')
  .addHelpText('after', `
Examples:
  Process exported sponsors data:
    $ GITHUB_API_KEY=your_token node scripts/gh.js

  Dry run to see what would be updated:
    $ GITHUB_API_KEY=your_token node scripts/gh.js --dry-run

  Generate both CSV and JSON output:
    $ GITHUB_API_KEY=your_token node scripts/gh.js --json

Environment Variables:
  GITHUB_API_KEY - Required GitHub Personal Access Token with 'user:read' scope

Note: This script processes manually exported sponsors data from GitHub Sponsors dashboard.
Export your sponsors data as JSON and place it in data/solnic-sponsorships-all-time.json
`)
  .parse();

const options = program.opts();

// Check for required environment variables and input file
const checkEnvironment = () => {
  if (!process.env.GITHUB_API_KEY) {
    console.error('❌ Missing required environment variable: GITHUB_API_KEY');
    console.error('💡 Get a token from: https://github.com/settings/tokens');
    console.error('💡 Required scopes: user:read');
    console.error('💡 Usage: GITHUB_API_KEY=your_token node scripts/gh.js');
    process.exit(1);
  }

  const inputPath = path.resolve(options.input);
  if (!fs.existsSync(inputPath)) {
    console.error(`❌ Input file not found: ${inputPath}`);
    console.error('💡 Export your sponsors data from GitHub Sponsors dashboard as JSON');
    console.error('💡 Place the file at: data/solnic-sponsorships-all-time.json');
    process.exit(1);
  }

  return { inputPath };
};

// Initialize GitHub API client
const initializeGitHubClient = () => {
  try {
    const gh = new GitHub({
      token: process.env.GITHUB_API_KEY
    });

    if (options.verbose) {
      console.log('✅ GitHub API client initialized');
      console.log('⚠️  Note: Sponsors data is not accessible via API');
    }

    return gh;
  } catch (error) {
    console.error('❌ Failed to initialize GitHub API client:', error.message);
    process.exit(1);
  }
};

// Get user information and validate API access
const validateAPIAccess = async (gh) => {
  try {
    console.log('📊 Validating GitHub API access...');

    const user = gh.getUser();
    const { data: userInfo } = await user.getProfile();

    if (options.verbose) {
      console.log(`✅ Authenticated as: ${userInfo.login} (${userInfo.name || 'No name'})`);
    }

    return userInfo;
  } catch (error) {
    if (error.response && error.response.status === 401) {
      throw new Error('GitHub API authentication failed. Check your GITHUB_API_KEY.');
    } else if (error.response && error.response.status === 403) {
      throw new Error('GitHub API rate limit exceeded or insufficient permissions.');
    } else {
      throw new Error(`GitHub API error: ${error.message}`);
    }
  }
};

// Fetch GitHub profile data for a sponsor
const fetchSponsorProfile = async (gh, handle) => {
  try {
    const user = gh.getUser(handle);
    const { data: profile } = await user.getProfile();

    return {
      login: profile.login,
      name: profile.name || profile.login,
      avatar_url: profile.avatar_url
    };
  } catch (error) {
    if (options.verbose) {
      console.warn(`⚠️  Could not fetch profile for ${handle}: ${error.message}`);
    }

    // Return fallback data
    return {
      login: handle,
      name: handle,
      avatar_url: `https://github.com/${handle}.png`
    };
  }
};

// Process sponsors data and fetch GitHub profiles
const processSponsorsData = async (gh, sponsorsData) => {
  console.log('🔍 Processing sponsors data and fetching GitHub profiles...');

  const processedSponsors = [];

  for (const sponsor of sponsorsData) {
    if (options.verbose) {
      console.log(`📄 Processing sponsor: ${sponsor.sponsor_handle}`);
    }

    // Fetch GitHub profile data
    const profile = await fetchSponsorProfile(gh, sponsor.sponsor_handle);

    // Determine if sponsor is active
    const isActive = isSponsorActive(sponsor);

    // Get latest tier info
    const tierInfo = getLatestTierInfo(sponsor);

    // Format sponsorship start date
    const startDate = new Date(sponsor.sponsorship_started_on).toISOString().replace('T', ' ').replace(/\.\d{3}Z$/, '');

    processedSponsors.push({
      sponsor_handle: profile.login,
      sponsor_name: profile.name,
      avatar_url: profile.avatar_url,
      is_active: isActive,
      sponsorship_started_on: startDate,
      tier_name: tierInfo.tier_name,
      tier_monthly_price_in_dollars: tierInfo.tier_monthly_price_in_dollars
    });

    // Add small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log(`✅ Processed ${processedSponsors.length} sponsors`);
  return processedSponsors;
};

// Read and parse the exported sponsors JSON file
const readSponsorsData = (inputPath) => {
  try {
    console.log(`📄 Reading sponsors data from: ${inputPath}`);
    const jsonContent = fs.readFileSync(inputPath, 'utf8');
    const sponsorsData = JSON.parse(jsonContent);

    if (!Array.isArray(sponsorsData)) {
      throw new Error('Expected sponsors data to be an array');
    }

    console.log(`✅ Found ${sponsorsData.length} sponsors in export file`);
    return sponsorsData;
  } catch (error) {
    console.error('❌ Failed to read sponsors data:', error.message);
    process.exit(1);
  }
};

// Determine if a sponsor is currently active based on recent transactions
const isSponsorActive = (sponsor) => {
  if (!sponsor.transactions || sponsor.transactions.length === 0) {
    return false;
  }

  // Sort transactions by date (most recent first)
  const sortedTransactions = sponsor.transactions
    .filter(t => t.status === 'settled' || t.status === 'credit_balance_adjusted')
    .sort((a, b) => new Date(b.transaction_date) - new Date(a.transaction_date));

  if (sortedTransactions.length === 0) {
    return false;
  }

  // Check if the most recent transaction was within the last 45 days
  const mostRecentTransaction = sortedTransactions[0];
  const transactionDate = new Date(mostRecentTransaction.transaction_date);
  const now = new Date();
  const daysSinceLastTransaction = (now - transactionDate) / (1000 * 60 * 60 * 24);

  return daysSinceLastTransaction <= 45;
};

// Get the most recent tier information for a sponsor
const getLatestTierInfo = (sponsor) => {
  if (!sponsor.transactions || sponsor.transactions.length === 0) {
    return { tier_name: '', tier_monthly_price_in_dollars: '' };
  }

  // Sort transactions by date (most recent first)
  const sortedTransactions = sponsor.transactions
    .filter(t => t.status === 'settled' || t.status === 'credit_balance_adjusted')
    .sort((a, b) => new Date(b.transaction_date) - new Date(a.transaction_date));

  if (sortedTransactions.length === 0) {
    return { tier_name: '', tier_monthly_price_in_dollars: '' };
  }

  const latestTransaction = sortedTransactions[0];
  return {
    tier_name: latestTransaction.tier_name || '',
    tier_monthly_price_in_dollars: latestTransaction.tier_monthly_amount ?
      latestTransaction.tier_monthly_amount.replace('$', '') : ''
  };
};

// Format sponsor data for CSV output
const formatSponsorDataForCSV = (sponsorsData) => {
  return sponsorsData.map(sponsor => ({
    sponsor_handle: sponsor.sponsor_handle,
    sponsor_name: sponsor.sponsor_name,
    avatar_url: sponsor.avatar_url,
    is_active: sponsor.is_active ? 'true' : 'false',
    sponsorship_started_on: sponsor.sponsorship_started_on,
    tier_name: sponsor.tier_name,
    tier_monthly_price_in_dollars: sponsor.tier_monthly_price_in_dollars
  }));
};

// Convert data to CSV format
const generateCSV = (data) => {
  const headers = [
    'sponsor_handle',
    'sponsor_name',
    'avatar_url',
    'is_active',
    'sponsorship_started_on',
    'tier_name',
    'tier_monthly_price_in_dollars'
  ];

  const csvLines = [headers.join(',')];

  data.forEach(row => {
    const csvRow = headers.map(header => {
      const value = row[header] || '';
      // Escape commas and quotes in CSV values
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    });
    csvLines.push(csvRow.join(','));
  });

  return csvLines.join('\n');
};

// Generate JSON content from sponsor data
const generateJSON = (sponsorData) => {
  const activeSponsors = sponsorData.filter(s => s.is_active);
  const pastSponsors = sponsorData.filter(s => !s.is_active);

  return JSON.stringify({
    generated_at: new Date().toISOString(),
    total_sponsors: sponsorData.length,
    active_sponsors: activeSponsors.length,
    past_sponsors: pastSponsors.length,
    sponsors: {
      active: activeSponsors,
      past: pastSponsors,
      all: sponsorData
    }
  }, null, 2);
};



// Main function
const main = async () => {
  try {
    console.log('🚀 Starting GitHub sponsors data processing...');

    // Check environment and get input path
    const { inputPath } = checkEnvironment();

    // Initialize GitHub API client
    const gh = initializeGitHubClient();

    // Validate API access
    await validateAPIAccess(gh);

    // Read exported sponsors data
    const rawSponsorsData = readSponsorsData(inputPath);

    // Process sponsors data and fetch GitHub profiles
    const processedSponsors = await processSponsorsData(gh, rawSponsorsData);

    // Format the data for CSV
    const formattedData = formatSponsorDataForCSV(processedSponsors);

    // Generate CSV and JSON content
    const csvContent = generateCSV(formattedData);
    const jsonContent = options.json ? generateJSON(processedSponsors) : null;

    // Count active vs inactive
    const activeCount = processedSponsors.filter(s => s.is_active).length;
    const inactiveCount = processedSponsors.filter(s => !s.is_active).length;

    // Prepare output paths
    const csvOutputPath = path.resolve(options.output);
    const jsonOutputPath = options.json ? csvOutputPath.replace('.csv', '.json') : null;

    if (options.dryRun) {
      console.log('\n🔍 Dry run - would write files:');
      console.log(`📊 Total sponsors: ${processedSponsors.length}`);
      console.log(`📊 Active sponsors: ${activeCount}`);
      console.log(`📊 Past sponsors: ${inactiveCount}`);
      console.log(`📄 CSV output file: ${csvOutputPath}`);
      if (jsonOutputPath) {
        console.log(`📄 JSON output file: ${jsonOutputPath}`);
      }
      console.log('\n🔍 CSV preview (first 5 lines):');
      console.log(csvContent.split('\n').slice(0, 5).join('\n'));
      console.log('\n🔍 Dry run completed - no files were written');
      return;
    }

    // Write the CSV file
    fs.writeFileSync(csvOutputPath, csvContent);
    console.log('✅ GitHub sponsors CSV updated successfully!');

    // Write the JSON file if requested
    if (options.json && jsonContent && jsonOutputPath) {
      fs.writeFileSync(jsonOutputPath, jsonContent);
      console.log('✅ GitHub sponsors JSON generated successfully!');
    }

    console.log(`📊 Total sponsors: ${processedSponsors.length}`);
    console.log(`📊 Active sponsors: ${activeCount}`);
    console.log(`📊 Past sponsors: ${inactiveCount}`);
    console.log(`💾 CSV saved to: ${csvOutputPath}`);
    if (jsonOutputPath) {
      console.log(`💾 JSON saved to: ${jsonOutputPath}`);
    }

    if (options.verbose) {
      console.log('\n📋 Next steps:');
      console.log('1. Review the generated files');
      console.log('2. Run: node scripts/ghost.js --page github-sponsors');
      console.log('3. Your sponsors page will be updated with the latest data');
      console.log('\n💡 To update sponsors data in the future:');
      console.log('   - Export fresh data from GitHub Sponsors dashboard');
      console.log('   - Replace the JSON file and run this script again');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);

    if (options.verbose) {
      console.error('Stack trace:', error.stack);
    }

    process.exit(1);
  }
};

// Run the script
if (require.main === module) {
  main();
}
