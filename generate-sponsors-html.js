#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Parse CSV data
const parseCSV = (csvPath) => {
  const content = fs.readFileSync(csvPath, 'utf8');
  const lines = content.trim().split('\n');
  const headers = lines[0].split(',');

  return lines.slice(1).map(line => {
    const values = line.split(',');
    const row = {};
    headers.forEach((header, index) => {
      row[header] = values[index] || '';
    });
    return row;
  });
};

// Generate GitHub profile URL
const githubProfileUrl = (handle) => {
  return `https://github.com/${handle}`;
};

// Generate GitHub Sponsors page HTML with embedded CSS
const generateGitHubSponsorsHTML = () => {
  const csvPath = path.join(__dirname, 'data', 'github-sponsors.csv');

  if (!fs.existsSync(csvPath)) {
    console.error(`❌ GitHub sponsors CSV file not found: ${csvPath}`);
    process.exit(1);
  }

  console.log('📊 Reading GitHub sponsors data...');
  const sponsorsData = parseCSV(csvPath);

  // Process sponsors data
  const currentSponsors = sponsorsData
    .filter(sponsor => sponsor.is_active === 'true')
    .sort((a, b) => new Date(a.sponsorship_started_on) - new Date(b.sponsorship_started_on))
    .reverse();

  const pastSponsors = sponsorsData
    .filter(sponsor => sponsor.is_active === 'false')
    .sort((a, b) => new Date(a.sponsorship_started_on) - new Date(b.sponsorship_started_on))
    .reverse();

  // Generate complete HTML with embedded CSS
  let html = '';

  // Add embedded CSS
  html += '<style>\n';
  html += '.github-sponsors-page {\n';
  html += '  max-width: 800px;\n';
  html += '  margin: 0 auto;\n';
  html += '}\n\n';

  html += '.github-sponsors-page h2 {\n';
  html += '  margin: 2rem 0 1rem 0;\n';
  html += '  font-size: 1.8rem;\n';
  html += '  font-weight: 700;\n';
  html += '}\n\n';

  html += '.github-sponsors-page p {\n';
  html += '  margin: 1rem 0;\n';
  html += '  line-height: 1.6;\n';
  html += '}\n\n';

  html += '.sponsors-grid {\n';
  html += '  display: grid;\n';
  html += '  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n';
  html += '  gap: 1.5rem;\n';
  html += '  margin: 2rem 0;\n';
  html += '}\n\n';

  html += '.sponsor-card {\n';
  html += '  border: 1px solid rgba(0,0,0,0.1);\n';
  html += '  border-radius: 12px;\n';
  html += '  padding: 0;\n';
  html += '  background: #fff;\n';
  html += '  transition: transform 0.2s ease, box-shadow 0.2s ease;\n';
  html += '  overflow: hidden;\n';
  html += '}\n\n';

  html += '.sponsor-card:hover {\n';
  html += '  transform: translateY(-2px);\n';
  html += '  box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n';
  html += '}\n\n';

  html += '.sponsor-link {\n';
  html += '  display: flex;\n';
  html += '  align-items: center;\n';
  html += '  gap: 1rem;\n';
  html += '  padding: 1rem;\n';
  html += '  text-decoration: none;\n';
  html += '  color: inherit;\n';
  html += '}\n\n';

  html += '.sponsor-avatar {\n';
  html += '  width: 60px;\n';
  html += '  height: 60px;\n';
  html += '  border-radius: 50%;\n';
  html += '  object-fit: cover;\n';
  html += '  flex-shrink: 0;\n';
  html += '}\n\n';

  html += '.sponsor-info h3 {\n';
  html += '  margin: 0;\n';
  html += '  font-size: 1.1rem;\n';
  html += '  font-weight: 600;\n';
  html += '}\n\n';

  html += '.sponsors-list {\n';
  html += '  display: flex;\n';
  html += '  flex-wrap: wrap;\n';
  html += '  gap: 0.5rem;\n';
  html += '  margin: 2rem 0;\n';
  html += '}\n\n';

  html += '.sponsor-item {\n';
  html += '  display: inline-flex;\n';
  html += '  align-items: center;\n';
  html += '  gap: 0.5rem;\n';
  html += '  padding: 0.5rem 0.75rem;\n';
  html += '  background: rgba(0,0,0,0.05);\n';
  html += '  border-radius: 20px;\n';
  html += '  text-decoration: none;\n';
  html += '  color: inherit;\n';
  html += '  font-size: 0.9rem;\n';
  html += '  transition: background-color 0.2s ease;\n';
  html += '}\n\n';

  html += '.sponsor-item:hover {\n';
  html += '  background: rgba(0,0,0,0.1);\n';
  html += '}\n\n';

  html += '.sponsor-avatar-small {\n';
  html += '  width: 24px;\n';
  html += '  height: 24px;\n';
  html += '  border-radius: 50%;\n';
  html += '  object-fit: cover;\n';
  html += '}\n\n';

  html += '@media (prefers-color-scheme: dark) {\n';
  html += '  .sponsor-card {\n';
  html += '    background: #1a1a1a;\n';
  html += '    border-color: rgba(255,255,255,0.1);\n';
  html += '  }\n';
  html += '  .sponsor-item {\n';
  html += '    background: rgba(255,255,255,0.1);\n';
  html += '  }\n';
  html += '  .sponsor-item:hover {\n';
  html += '    background: rgba(255,255,255,0.2);\n';
  html += '  }\n';
  html += '}\n';
  html += '</style>\n\n';

  // Page wrapper
  html += '<div class="github-sponsors-page">\n';
  html += `  <!-- Generated: ${new Date().toISOString()} -->\n`;
  html += '  <h2>Current Sponsors</h2>\n';
  html += '  <p>I\'m incredibly grateful for the support from my current GitHub Sponsors. Your contributions help me dedicate more time to open source work and make it sustainable. Thank you! 🙏</p>\n';

  if (currentSponsors.length > 0) {
    html += '  <div class="sponsors-grid">\n';
    currentSponsors.forEach(sponsor => {
      const profileUrl = githubProfileUrl(sponsor.sponsor_handle);
      const name = sponsor.sponsor_name || sponsor.sponsor_handle;
      html += '    <div class="sponsor-card">\n';
      html += `      <a href="${profileUrl}" target="_blank" rel="noopener" class="sponsor-link">\n`;
      if (sponsor.avatar_url) {
        html += `        <img src="${sponsor.avatar_url}" alt="${name}" class="sponsor-avatar">\n`;
      }
      html += '        <div class="sponsor-info">\n';
      html += `          <h3>${name}</h3>\n`;
      html += '        </div>\n';
      html += '      </a>\n';
      html += '    </div>\n';
    });
    html += '  </div>\n';
  } else {
    html += '  <p><em>No current sponsors at the moment.</em></p>\n';
  }

  html += '  <h2>Past Sponsors</h2>\n';
  html += '  <p>Thank you to all my past sponsors for their support! 🙏</p>\n';

  if (pastSponsors.length > 0) {
    html += '  <div class="sponsors-list">\n';
    pastSponsors.forEach(sponsor => {
      const profileUrl = githubProfileUrl(sponsor.sponsor_handle);
      const name = sponsor.sponsor_name || sponsor.sponsor_handle;
      const handle = sponsor.sponsor_handle;
      html += `    <a href="${profileUrl}" target="_blank" rel="noopener" class="sponsor-item">\n`;
      if (sponsor.avatar_url) {
        html += `      <img src="${sponsor.avatar_url}" alt="${name}" class="sponsor-avatar-small">\n`;
      }
      html += `      <span>@${handle}</span>\n`;
      html += '    </a>\n';
    });
    html += '  </div>\n';
  } else {
    html += '  <p><em>No past sponsors.</em></p>\n';
  }

  html += '</div>\n';

  return {
    html,
    currentSponsors: currentSponsors.length,
    pastSponsors: pastSponsors.length
  };
};

// Main function
const main = () => {
  console.log('🚀 Generating GitHub Sponsors HTML...');
  
  const { html, currentSponsors, pastSponsors } = generateGitHubSponsorsHTML();
  const outputFile = 'github-sponsors-manual-update.html';

  try {
    fs.writeFileSync(outputFile, html);
    console.log('✅ HTML with embedded CSS generated successfully!');
    console.log(`💾 Output saved to: ${outputFile}`);
    console.log(`📊 Current sponsors: ${currentSponsors}`);
    console.log(`📊 Past sponsors: ${pastSponsors}`);
    console.log('\n📋 Manual Update Instructions:');
    console.log('1. Copy the entire content from the generated HTML file');
    console.log('2. Go to Ghost.io Admin → Pages → GitHub Sponsors');
    console.log('3. Delete current content and add an HTML card');
    console.log('4. Paste the HTML content into the HTML card');
    console.log('5. Publish the page');
  } catch (writeError) {
    console.error('❌ Error writing HTML file:', writeError.message);
    process.exit(1);
  }
};

// Run the script
if (require.main === module) {
  main();
}
